�f�5            �f�5            �f�5            �"��           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�10�܆`X          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (�ۣ�10mў            
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10`N~��           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10�R6�`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10�|��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�105�~�6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10A
�a          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�107&vW	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10����� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10�+�.1           	39_config
��؈��O�ԓ �ۣ�1��CE~           	39_configf
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1*,�� 
          	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ�������           	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ����
ۯ��Њ���ԓ �ۣ�1^0?�;          	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ����
ۯ��Њ���ԓ �ۣ�1
��՛�����ԓ �ۣ�1
���Åօ�C�ԓ �ۣ�1
�����Ӆ���ԓ �ۣ�1
��������_�ԓ �ۣ�1|z�          	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ����
ۯ��Њ���ԓ �ۣ�1
��՛�����ԓ �ۣ�1
���Åօ�C�ԓ �ۣ�1
�����Ӆ���ԓ �ۣ�1
��������_�ԓ �ۣ�1
�����������I �ۣ�1
�������I �ۣ�1
ʒ���қ�C��I �ۣ�1
���޾���,��I �ۣ�1
���������I �ۣ�1-MQP          	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ����
ۯ��Њ���ԓ �ۣ�1
��՛�����ԓ �ۣ�1
���Åօ�C�ԓ �ۣ�1
�����Ӆ���ԓ �ۣ�1
��������_�ԓ �ۣ�1
�����������I �ۣ�1
�������I �ۣ�1
ʒ���қ�C��I �ۣ�1
���޾���,��I �ۣ�1
���������I �ۣ�1
������t�ԓ �ۣ�1
��������k�ԓ �ۣ�1
գ��������ԓ �ۣ�1
��ר�ٳ���ԓ �ۣ�1
ෛ�������ԓ �ۣ�1
������Ʉ��ԓ �ۣ�1��C	�          	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ����
ۯ��Њ���ԓ �ۣ�1
��՛�����ԓ �ۣ�1
���Åօ�C�ԓ �ۣ�1
�����Ӆ���ԓ �ۣ�1
��������_�ԓ �ۣ�1
�����������I �ۣ�1
�������I �ۣ�1
ʒ���қ�C��I �ۣ�1
���޾���,��I �ۣ�1
���������I �ۣ�1
������t�ԓ �ۣ�1
��������k�ԓ �ۣ�1
գ��������ԓ �ۣ�1
��ר�ٳ���ԓ �ۣ�1
ෛ�������ԓ �ۣ�1
������Ʉ��ԓ �ۣ�1
"��ї�Z�ԓ �ۣ�1(Ȏ�������
#�򖐩�����ԓ �ۣ�1(Ȏ�������
#�ɕԺ����ԓ �ۣ�1(Ȏ�������ka��          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10G��4           20_1_1
1�G%            �T�z�       .   4_EsbDownloadRowPromo
EsbDownloadRowPromo��4_IPH_BatterySaverMode
IPH_BatterySaverMode��4_IPH_CompanionSidePanel
IPH_CompanionSidePanel��$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch��4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome��4_IPH_DiscardRing
IPH_DiscardRing��4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo��/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered��4_IPH_HistorySearch
IPH_HistorySearch��&4_IPH_FocusHelpBubbleScreenReaderPromo*
$IPH_FocusHelpBubbleScreenReaderPromo��4_IPH_GMCCastStartStop
IPH_GMCCastStartStop��4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting��4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode��4_IPH_LiveCaption
IPH_LiveCaption��(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave��+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin��"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch��4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature��4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature��*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature��-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature��&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature��-4_IPH_PriceTrackingPageActionIconLabelFeature1
+IPH_PriceTrackingPageActionIconLabelFeature��4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel��4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature��%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature��)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature��14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature��4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept��4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro��4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup��4_IPH_ProfileSwitch
IPH_ProfileSwitch��4_IPH_PriceTrackingInSidePanel"
IPH_PriceTrackingInSidePanel��4_IPH_PwaQuietNotification
IPH_PwaQuietNotification��4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn��'4_IPH_AutofillBnplAffirmOrZipSuggestion+
%IPH_AutofillBnplAffirmOrZipSuggestion��.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion��&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion��#4_IPH_AutofillVirtualCardSuggestion'
!IPH_AutofillVirtualCardSuggestion��4_IPH_CookieControls
IPH_CookieControls��$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch��,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab��!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin��4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop��4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop��4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop��\"^QCC          &9_5b600c71-eded-450a-8976-a809268da17b�	$5b600c71-eded-450a-8976-a809268da17b��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\5b600c71-eded-450a-8976-a809268da17b8�֪�ě�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION���fD          &9_e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�	$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf8�ת�ě�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSjG��=E          &9_85d94959-fe22-4417-a48b-98b2eed235bb�	$85d94959-fe22-4417-a48b-98b2eed235bb��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\85d94959-fe22-4417-a48b-98b2eed235bb8�ת�ě�@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY�Ϫ�2F          &9_4e2112aa-c379-433b-89d2-3b46c090d520�	$4e2112aa-c379-433b-89d2-3b46c090d520��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4e2112aa-c379-433b-89d2-3b46c090d5208�ت�ě�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2����dG          &9_4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�	$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4a0a2ce2-6106-4bd6-8d4f-7132f993f8a98�ت�ě�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS|��[H          &9_6f5c3b20-bd2d-44ce-a862-a6881455cfee�	$6f5c3b20-bd2d-44ce-a862-a6881455cfee��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6f5c3b20-bd2d-44ce-a862-a6881455cfee8�ت�ě�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTa	h&GI          &9_91a180a1-637a-4b8f-88d4-169d61abf733�	$91a180a1-637a-4b8f-88d4-169d61abf733��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\91a180a1-637a-4b8f-88d4-169d61abf7338�٪�ě�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�`�NEJ          &9_163b2ad6-1b2d-49d4-bef2-90238f0d103e�	$163b2ad6-1b2d-49d4-bef2-90238f0d103e��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\163b2ad6-1b2d-49d4-bef2-90238f0d103e8�٪�ě�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�
�^K          &9_a34403c9-a55f-4129-a537-ae96ef1b2c76�	$a34403c9-a55f-4129-a537-ae96ef1b2c76��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\a34403c9-a55f-4129-a537-ae96ef1b2c768�٪�ě�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONȝBCL          &9_5b600c71-eded-450a-8976-a809268da17b�	$5b600c71-eded-450a-8976-a809268da17b��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\5b600c71-eded-450a-8976-a809268da17b8�֪�ě�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION� KCM          &9_5b600c71-eded-450a-8976-a809268da17b�	$5b600c71-eded-450a-8976-a809268da17b��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\5b600c71-eded-450a-8976-a809268da17b8�֪�ě�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��CN          &9_5b600c71-eded-450a-8976-a809268da17b�	$5b600c71-eded-450a-8976-a809268da17b��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\5b600c71-eded-450a-8976-a809268da17b8�֪�ě�@ HPϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�U�fO          &9_e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�	$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf8�ת�ě�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSc*�fP          &9_e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�	$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf8�ת�ě�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�  �fQ          &9_e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�	$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf8�ת�ě�@ HPϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�#��=R          &9_85d94959-fe22-4417-a48b-98b2eed235bb�	$85d94959-fe22-4417-a48b-98b2eed235bb��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\85d94959-fe22-4417-a48b-98b2eed235bb8�ת�ě�@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITYd���2S          &9_4e2112aa-c379-433b-89d2-3b46c090d520�	$4e2112aa-c379-433b-89d2-3b46c090d520��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4e2112aa-c379-433b-89d2-3b46c090d5208�ت�ě�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�b�
dT          &9_4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�	$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4a0a2ce2-6106-4bd6-8d4f-7132f993f8a98�ت�ě�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSS��[U          &9_6f5c3b20-bd2d-44ce-a862-a6881455cfee�	$6f5c3b20-bd2d-44ce-a862-a6881455cfee��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6f5c3b20-bd2d-44ce-a862-a6881455cfee8�ت�ě�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST���rGV          &9_91a180a1-637a-4b8f-88d4-169d61abf733�	$91a180a1-637a-4b8f-88d4-169d61abf733��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\91a180a1-637a-4b8f-88d4-169d61abf7338�٪�ě�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�_�EW          &9_163b2ad6-1b2d-49d4-bef2-90238f0d103e�	$163b2ad6-1b2d-49d4-bef2-90238f0d103e��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\163b2ad6-1b2d-49d4-bef2-90238f0d103e8�٪�ě�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGr���^X          &9_a34403c9-a55f-4129-a537-ae96ef1b2c76�	$a34403c9-a55f-4129-a537-ae96ef1b2c76��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\a34403c9-a55f-4129-a537-ae96ef1b2c768�٪�ě�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION̒��Y          021_download,e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�
�
$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     �T��Z          &9_e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�	$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf8�ת�ě�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH89XLHGQMEYjtlssd7oqjKw2Sw04FG21BxNGQbDAecTnaX7RJ5g3kdw300ARQEhb_O6l accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:52 GMT expires:Fri, 30 May 2025 13:09:52 GMT x-goog-hash:crc32c=KLQV2Q== content-length:5158 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS
�~/�[          021_download,5b600c71-eded-450a-8976-a809268da17b�
�
$5b600c71-eded-450a-8976-a809268da17b
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     021_download,e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�
�
$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj       r       x�(����������������� S,
�
��Ȭk�.-��[�@��u�Ka���i"��� � � � � � � ����������������������


     x:�pa]          &9_5b600c71-eded-450a-8976-a809268da17b�	$5b600c71-eded-450a-8976-a809268da17b��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\5b600c71-eded-450a-8976-a809268da17b8�֪�ě�@ HPϟ�/X ` p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88axyPmYgYw9_57_SRLdrym9gNQkxODTRv9rMI5nnhNBSKKCT5BfwmR2PwAl8N-bBDb expires:Fri, 30 May 2025 13:09:52 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:52 GMT x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONa{�.=^          021_download,e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�
�
$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   9 5 7 1 3 8 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e 0 0 b a 5 b f - 7 b b 7 - 4 f 1 f - b e 5 9 - e c 9 f b e 6 b f c a f   x�(����������������� S,
�
��Ȭk�.-��[�@��u�Ka���i"��� �� � � � � ����������������������


     l��_          021_download,5b600c71-eded-450a-8976-a809268da17b�
�
$5b600c71-eded-450a-8976-a809268da17b
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   8 2 8 6 5 8 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 6 0 0 c 7 1 - e d e d - 4 5 0 a - 8 9 7 6 - a 8 0 9 2 6 8 d a 1 7 b   x ����������������� �� �� � � � � ����������������������


     K�c k`          021_download,5b600c71-eded-450a-8976-a809268da17b�
�
$5b600c71-eded-450a-8976-a809268da17b
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   8 2 8 6 5 8 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 6 0 0 c 7 1 - e d e d - 4 5 0 a - 8 9 7 6 - a 8 0 9 2 6 8 d a 1 7 b   x������������������ �!��Q�k���֧*��:{`B��aOW�+V�@�� �� � � � � ����������������������


     021_download,e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�
�
$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e 0 0 b a 5 b f - 7 b b 7 - 4 f 1 f - b e 5 9 - e c 9 f b e 6 b f c a f   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e 0 0 b a 5 b f - 7 b b 7 - 4 f 1 f - b e 5 9 - e c 9 f b e 6 b f c a f   x�(������������� S,
�
��Ȭk�.-��[�@��u�Ka���i"����� � � � � ����������������������


     jb�b          &9_e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�	$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf8�ת�ě�@����ě�HPϟ�/X�(`����ě�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH89XLHGQMEYjtlssd7oqjKw2Sw04FG21BxNGQbDAecTnaX7RJ5g3kdw300ARQEhb_O6l accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:52 GMT expires:Fri, 30 May 2025 13:09:52 GMT x-goog-hash:crc32c=KLQV2Q== content-length:5158 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��˔� c          &9_85d94959-fe22-4417-a48b-98b2eed235bb�	$85d94959-fe22-4417-a48b-98b2eed235bb��������  ( "�
phttps://optimizationguide-pa.googleapis.com/dow��$�nloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\85d94959-fe22-4417-a48b-98b2eed235bb8�ת�ě�@ H Pϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY@A=d          &9_85d94959-fe22-4417-a48b-98b2eed235bb�	$85d94959-fe22-4417-a48b-98b2eed235bb��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\85d94959-fe22-4417-a48b-98b2eed235bb8�ת�ě�@ HPϟ�/X ` p x � ��U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY�IJe          021_download,e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�
�
$e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062323&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�(Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e 0 0 b a 5 b f - 7 b b 7 - 4 f 1 f - b e 5 9 - e c 9 f b e 6 b f c a f   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e 0 0 b a 5 b f - 7 b b 7 - 4 f 1 f - b e 5 9 - e c 9 f b e 6 b f c a f   x�(������������� S,
�
��Ȭk�.-��[�@��u�Ka���i"����� � � � � ����������������������


     ���> f           021_download,e00ba5bf-7bb7-4f1f-be59-ec9fbe6bfcaf�,��:g          021_download,5b600c71-eded-450a-8976-a809268da17b�
�
$5b600c71-eded-450a-8976-a809268da17b
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 6 0 0 c 7 1 - e d e d - 4 5 0 a - 8 9 7 6 - a 8 0 9 2 6 8 d a 1 7 b   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 6 0 0 c 7 1 - e d e d - 4 5 0 a - 8 9 7 6 - a 8 0 9 2 6 8 d a 1 7 b   x�������������� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     y�+�qh          &9_5b600c71-eded-450a-8976-a809268da17b�	$5b600c71-eded-450a-8976-a809268da17b��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\5b600c71-eded-450a-8976-a809268da17b8�֪�ě�@˒��ě�HPϟ�/X�`˒��ě�p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88axyPmYgYw9_57_SRLdrym9gNQkxODTRv9rMI5nnhNBSKKCT5BfwmR2PwAl8N-bBDb expires:Fri, 30 May 2025 13:09:52 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:52 GMT x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��Ec2i          &9_4e2112aa-c379-433b-89d2-3b46c090d520�	$4e2112aa-c379-433b-89d2-3b46c090d520��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4e2112aa-c379-433b-89d2-3b46c090d5208�ت�ě�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2ɿ��2j          &9_4e2112aa-c379-433b-89d2-3b46c090d520�	$4e2112aa-c379-433b-89d2-3b46c090d520��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4e2112aa-c379-433b-89d2-3b46c090d5208�ت�ě�@ HPϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2q�\:k          021_download,5b600c71-eded-450a-8976-a809268da17b�
�
$5b600c71-eded-450a-8976-a809268da17b
���������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 6 0 0 c 7 1 - e d e d - 4 5 0 a - 8 9 7 6 - a 8 0 9 2 6 8 d a 1 7 b   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 6 0 0 c 7 1 - e d e d - 4 5 0 a - 8 9 7 6 - a 8 0 9 2 6 8 d a 1 7 b   x�������������� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     7@��> l           021_download,5b600c71-eded-450a-8976-a809268da17bً��m          021_download,85d94959-fe22-4417-a48b-98b2eed235bb�
�
$85d94959-fe22-4417-a48b-98b2eed235bb
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     �ֆXn          &9_85d94959-fe22-4417-a48b-98b2eed235bb�	$85d94959-fe22-4417-a48b-98b2eed235bb��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\85d94959-fe22-4417-a48b-98b2eed235bb8�ת�ě�@ HPϟ�/X ` p x �phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-JAaWZ5XRtUdIArsD23qGiSktmPWjOtSCuSnwhTDIznie8uw-R5c_6Ji2qXMecbfaG cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:53 GMT expires:Fri, 30 May 2025 13:09:53 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=phKVxw== content-length:883094 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITYo�o          021_download,85d94959-fe22-4417-a48b-98b2eed235bb�
�
$85d94959-fe22-4417-a48b-98b2eed235bb
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   4 7 4 9 9 3 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 8 5 d 9 4 9 5 9 - f e 2 2 - 4 4 1 7 - a 4 8 b - 9 8 b 2 e e d 2 3 5 b b   x ����������������� �� �� � � � � ����������������������


     R�%�p          021_download,4e2112aa-c379-433b-89d2-3b46c090d520�
�
$4e2112aa-c379-433b-89d2-3b46c090d520
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     �?=�-q          &9_4e2112aa-c379-433b-89d2-3b46c090d520�	$4e2112aa-c379-433b-89d2-3b46c090d520��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4e2112aa-c379-433b-89d2-3b46c090d5208�ت�ě�@ HPϟ�/X ` p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH89uFjr_ldj-wJZIu5qL7QBSifCHnivSFZWlRQp62ZrLqOqyJUWLVyLILZjmMnX8AgVZ vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:53 GMT expires:Fri, 30 May 2025 13:09:53 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2NɥK�r          021_download,4e2112aa-c379-433b-89d2-3b46c090d520�
�
$4e2112aa-c379-433b-89d2-3b46c090d520
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   6 1 5 8 9 9 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 2 1 1 2 a a - c 3 7 9 - 4 3 3 b - 8 9 d 2 - 3 b 4 6 c 0 9 0 d 5 2 0   x ����������������� �� �� � � � � ����������������������


     �j��7s          021_download,85d94959-fe22-4417-a48b-98b2eed235bb�
�
$85d94959-fe22-4417-a48b-98b2eed235bb
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 8 5 d 9 4 9 5 9 - f e 2 2 - 4 4 1 7 - a 4 8 b - 9 8 b 2 e e d 2 3 5 b b   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 8 5 d 9 4 9 5 9 - f e 2 2 - 4 4 1 7 - a 4 8 b - 9 8 b 2 e e d 2 3 5 b b   x��5������������� �x����c��[h:�S�Q���;"�?Ι#��5���� � � � � ����������������������


     �Ã�ht          &9_85d94959-fe22-4417-a48b-98b2eed235bb�	$85d94959-fe22-4417-a48b-98b2eed235bb��������  ( "�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITYGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\85d94959-fe22-4417-a48b-98b2eed235bb8�ת�ě�@����ě�HPϟ�/X��5`����ě�p x �phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-JAaWZ5XRtUdIArsD23qGiSktmPWjOtSCuSnwhTDIznie8uw-R5c_6Ji2qXMecbfaG cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:53 GMT expires:Fri, 30 May 2025 13:09:53 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=phKVxw== content-length:883094 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���U
.PredictionModelOptimizationTargetCustomDataKey#OPTIMIZATION_TARGET_PAGE_VISIBILITY4զdu          &9_4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�	$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4a0a2ce2-6106-4bd6-8d4f-7132f993f8a98�ت�ě�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS%�Րdv          &9_4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�	$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4a0a2ce2-6106-4bd6-8d4f-7132f993f8a98�ت�ě�@ HPϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�>�77w          021_download,85d94959-fe22-4417-a48b-98b2eed235bb�
�
$85d94959-fe22-4417-a48b-98b2eed235bb
���������"�
phttps://optimizationguide-pa.googleapis.com/downloads?name=1673999601&target=OPTIMIZATION_TARGET_PAGE_VISIBILITY " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��5Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 8 5 d 9 4 9 5 9 - f e 2 2 - 4 4 1 7 - a 4 8 b - 9 8 b 2 e e d 2 3 5 b b   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 8 5 d 9 4 9 5 9 - f e 2 2 - 4 4 1 7 - a 4 8 b - 9 8 b 2 e e d 2 3 5 b b   x��5������������� �x����c��[h:�S�Q���;"�?Ι#��5���� � � � � ����������������������


     mlg"> x           021_download,85d94959-fe22-4417-a48b-98b2eed235bb1�.��y          021_download,4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�
�
$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     021_download,4e2112aa-c379-433b-89d2-3b46c090d520�
�
$4e2112aa-c379-433b-89d2-3b46c090d520
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   6 1 5 8 9 9 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 2 1 1 2 a a - c 3 7 9 - 4 3 3 b - 8 9 d 2 - 3 b 4 6 c 0 9 0 d 5 2 0   x�������������������� {�?�Ǩ�O�ƺ����f.RMH����=��� �� � � � � ����������������������


     �x�i�{          &9_4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�	$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4a0a2ce2-6106-4bd6-8d4f-7132f993f8a98�ت�ě�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-aon9jdYNi1XWduNknT0w9mA4rmbj1lDABWqMZDVb1lO5cC-xcsMNlh1rpZnJxkg2m vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:53 GMT expires:Fri, 30 May 2025 13:09:53 GMT accept-ranges:bytes x-goog-hash:crc32c=HZgoPg== content-length:45166 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSq9{>|          021_download,4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�
�
$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   6 4 3 4 4 6 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 a 0 a 2 c e 2 - 6 1 0 6 - 4 b d 6 - 8 d 4 f - 7 1 3 2 f 9 9 3 f 8 a 9   x������������������� �� ��0{n��3=�����P��r�w@��à� �� � � � � ����������������������


     '`]K}          021_download,4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�
�
$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 a 0 a 2 c e 2 - 6 1 0 6 - 4 b d 6 - 8 d 4 f - 7 1 3 2 f 9 9 3 f 8 a 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 a 0 a 2 c e 2 - 6 1 0 6 - 4 b d 6 - 8 d 4 f - 7 1 3 2 f 9 9 3 f 8 a 9   x���������с���� �� ��0{n��3=�����P��r�w@��à��� � � � � ����������������������


     Ե�
�~          &9_4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�	$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4a0a2ce2-6106-4bd6-8d4f-7132f993f8a98�ت�ě�@����ě�HPϟ�/X��`����ě�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-aon9jdYNi1XWduNknT0w9mA4rmbj1lDABWqMZDVb1lO5cC-xcsMNlh1rpZnJxkg2m vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:53 GMT expires:Fri, 30 May 2025 13:09:53 GMT accept-ranges:bytes x-goog-hash:crc32c=HZgoPg== content-length:45166 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS����[          &9_6f5c3b20-bd2d-44ce-a862-a6881455cfee�	$6f5c3b20-bd2d-44ce-a862-a6881455cfee��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6f5c3b20-bd2d-44ce-a862-a6881455cfee8�ت�ě�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTy���[�          &9_6f5c3b20-bd2d-44ce-a862-a6881455cfee�	$6f5c3b20-bd2d-44ce-a862-a6881455cfee��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6f5c3b20-bd2d-44ce-a862-a6881455cfee8�ت�ě�@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTH��K�          021_download,4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9�
�
$4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1747062306&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 a 0 a 2 c e 2 - 6 1 0 6 - 4 b d 6 - 8 d 4 f - 7 1 3 2 f 9 9 3 f 8 a 9   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 a 0 a 2 c e 2 - 6 1 0 6 - 4 b d 6 - 8 d 4 f - 7 1 3 2 f 9 9 3 f 8 a 9   x���������с���� �� ��0{n��3=�����P��r�w@��à��� � � � � ����������������������


     �� �> �           021_download,4a0a2ce2-6106-4bd6-8d4f-7132f993f8a9ݯ�|/�          021_download,4e2112aa-c379-433b-89d2-3b46c090d520�
�
$4e2112aa-c379-433b-89d2-3b46c090d520
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 2 1 1 2 a a - c 3 7 9 - 4 3 3 b - 8 9 d 2 - 3 b 4 6 c 0 9 0 d 5 2 0   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 2 1 1 2 a a - c 3 7 9 - 4 3 3 b - 8 9 d 2 - 3 b 4 6 c 0 9 0 d 5 2 0   x���������������� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     \b
>�          &9_4e2112aa-c379-433b-89d2-3b46c090d520�	$4e2112aa-c379-433b-89d2-3b46c090d520��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\4e2112aa-c379-433b-89d2-3b46c090d5208�ت�ě�@����ě�HPϟ�/X���`����ě�p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH89uFjr_ldj-wJZIu5qL7QBSifCHnivSFZWlRQp62ZrLqOqyJUWLVyLILZjmMnX8AgVZ vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:53 GMT expires:Fri, 30 May 2025 13:09:53 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�aA�G�          &9_91a180a1-637a-4b8f-88d4-169d61abf733�	$91a180a1-637a-4b8f-88d4-169d61abf733��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\91a180a1-637a-4b8f-88d4-169d61abf7338�٪�ě�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGdz2G�          &9_91a180a1-637a-4b8f-88d4-169d61abf733�	$91a180a1-637a-4b8f-88d4-169d61abf733��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\91a180a1-637a-4b8f-88d4-169d61abf7338�٪�ě�@ HPϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGhБ/�          021_download,4e2112aa-c379-433b-89d2-3b46c090d520�
�
$4e2112aa-c379-433b-89d2-3b46c090d520
���������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 2 1 1 2 a a - c 3 7 9 - 4 3 3 b - 8 9 d 2 - 3 b 4 6 c 0 9 0 d 5 2 0   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 2 1 1 2 a a - c 3 7 9 - 4 3 3 b - 8 9 d 2 - 3 b 4 6 c 0 9 0 d 5 2 0   x���������������� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     ��c> �           021_download,4e2112aa-c379-433b-89d2-3b46c090d520�ռ��          021_download,6f5c3b20-bd2d-44ce-a862-a6881455cfee�
�
$6f5c3b20-bd2d-44ce-a862-a6881455cfee
���������"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x �Â�������������� �� � � � � � � ����������������������


     �:�o�          &9_6f5c3b20-bd2d-44ce-a862-a6881455cfee�	$6f5c3b20-bd2d-44ce-a862-a6881455cfee��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6f5c3b20-bd2d-44ce-a862-a6881455cfee8�ت�ě�@ HPϟ�/X ` p x �https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST��HTTP/1.1 200 x-guploader-uploadid:ABgVH8837yZmCPC3yeYAbI9NCHtR-gBoybVf5isY-ZlsmW9mUeF_4SXhtFJsc-rMHhUbfeDM vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:54 GMT expires:Fri, 30 May 2025 13:09:54 GMT accept-ranges:bytes x-goog-hash:crc32c=aDJGWw== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST�N���          021_download,91a180a1-637a-4b8f-88d4-169d61abf733�
�
$91a180a1-637a-4b8f-88d4-169d61abf733
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x �߂�������������� �� � � � � � � ����������������������


     �G��Q�          &9_91a180a1-637a-4b8f-88d4-169d61abf733�	$91a180a1-637a-4b8f-88d4-169d61abf733��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\91a180a1-637a-4b8f-88d4-169d61abf7338�٪�ě�@ HPϟ�/X ` p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH89xmmrVWmDl6Opp5iCMGBx80iVDXNrzC3KbwZ_zDkpnWci3M8HMqEAKqkQJJPMMhHe8 cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:54 GMT expires:Fri, 30 May 2025 13:09:54 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=yvBA+g== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING���ǈ �          021_download,6f5c3b20-bd2d-44ce-a862-a6881455cfee�
�
$6f5c3b20-bd2d-44ce-a862-a6881455cfee
���������"�
https://oud`͍ptimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   4 3 0 8 4 5 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 f 5 c 3 b 2 0 - b d 2 d - 4 4 c e - a 8 6 2 - a 6 8 8 1 4 5 5 c f e e   x �Â�������������� �� �� � � � � ����������������������


     ���          021_download,91a180a1-637a-4b8f-88d4-169d61abf733�
�
$91a180a1-637a-4b8f-88d4-169d61abf733
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   4 8 2 9 0 7 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 1 a 1 8 0 a 1 - 6 3 7 a - 4 b 8 f - 8 8 d 4 - 1 6 9 d 6 1 a b f 7 3 3   x �߂�������������� �� �� � � � � ����������������������


     ]��	m�          021_download,6f5c3b20-bd2d-44ce-a862-a6881455cfee�
�
$6f5c3b20-bd2d-44ce-a862-a6881455cfee
���������"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�ֶZ	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   4 3 0 8 4 5 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 f 5 c 3 b 2 0 - b d 2 d - 4 4 c e - a 8 6 2 - a 6 8 8 1 4 5 5 c f e e   x�ֶ�Â�������������� ݜ泮Y��'���<�/n�D��Y �4���� �� � � � � ����������������������


     021_download,91a180a1-637a-4b8f-88d4-169d61abf733�
�
$91a180a1-637a-4b8f-88d4-169d61abf733
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 1 a 1 8 0 a 1 - 6 3 7 a - 4 b 8 f - 8 8 d 4 - 1 6 9 d 6 1 a b f 7 3 3   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 1 a 1 8 0 a 1 - 6 3 7 a - 4 b 8 f - 8 8 d 4 - 1 6 9 d 6 1 a b f 7 3 3   x�֧�߂���������� a�"�*�gfI�9��MG�������ȁ��W�~��(���� � � � � ����������������������


     ��E�b�          &9_91a180a1-637a-4b8f-88d4-169d61abf733�	$91a180a1-637a-4b8f-88d4-169d61abf733��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\91a180a1-637a-4b8f-88d4-169d61abf7338�٪�ě�@����ě�HPϟ�/X�֧`����ě�p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH89xmmrVWmDl6Opp5iCMGBx80iVDXNrzC3KbwZ_zDkpnWci3M8HMqEAKqkQJJPMMhHe8 cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:54 GMT expires:Fri, 30 May 2025 13:09:54 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=yvBA+g== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING?��E�          &9_163b2ad6-1b2d-49d4-bef2-90238f0d103e�	$163b2ad6-1b2d-49d4-bef2-90238f0d103e��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\163b2ad6-1b2d-49d4-bef2-90238f0d103e8�٪�ě�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGg%�E�          &9_163b2ad6-1b2d-49d4-bef2-90238f0d103e�	$163b2ad6-1b2d-49d4-bef2-90238f0d103e��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\163b2ad6-1b2d-49d4-bef2-90238f0d103e8�٪�ě�@ HPϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING� 0�>�          021_download,91a180a1-637a-4b8f-88d4-169d61abf733�
�
$91a180a1-637a-4b8f-88d4-169d61abf733
���������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1747148602&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 1 a 1 8 0 a 1 - 6 3 7 a - 4 b 8 f - 8 8 d 4 - 1 6 9 d 6 1 a b f 7 3 3   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 1 a 1 8 0 a 1 - 6 3 7 a - 4 b 8 f - 8 8 d 4 - 1 6 9 d 6 1 a b f 7 3 3   x�֧�߂���������� a�"�*�gfI�9��MG�������ȁ��W�~��(���� � � � � ����������������������


     ���1> �           021_download,91a180a1-637a-4b8f-88d4-169d61abf733R<N�H�          021_download,6f5c3b20-bd2d-44ce-a862-a6881455cfee�
�
$6f5c3b20-bd2d-44ce-a862-a6881455cfee
���������"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�ֶZ	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 f 5 c 3 b 2 0 - b d 2 d - 4 4 c e - a 8 6 2 - a 6 8 8 1 4 5 5 c f e e   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 f 5 c 3 b 2 0 - b d 2 d - 4 4 c e - a 8 6 2 - a 6 8 8 1 4 5 5 c f e e   x�ֶ�Â���������� ݜ泮Y��'���<�/n�D��Y �4������ � � � � ����������������������


     �d����          &9_6f5c3b20-bd2d-44ce-a862-a6881455cfee�	$6f5c3b20-bd2d-44ce-a862-a6881455cfee��������  ( "�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGESTGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\6f5c3b20-bd2d-44ce-a862-a6881455cfee8�ت�ě�@���ě�HPϟ�/X�ֶ`���ě�p x �https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST��HTTP/1.1 200 x-guploader-uploadid:ABgVH8837yZmCPC3yeYAbI9NCHtR-gBoybVf5isY-ZlsmW9mUeF_4SXhtFJsc-rMHhUbfeDM vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:54 GMT expires:Fri, 30 May 2025 13:09:54 GMT accept-ranges:bytes x-goog-hash:crc32c=aDJGWw== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST���^�          &9_a34403c9-a55f-4129-a537-ae96ef1b2c76�	$a34403c9-a55f-4129-a537-ae96ef1b2c76��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\a34403c9-a55f-4129-a537-ae96ef1b2c768�٪�ě�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��F^�          &9_a34403c9-a55f-4129-a537-ae96ef1b2c76�	$a34403c9-a55f-4129-a537-ae96ef1b2c76��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\a34403c9-a55f-4129-a537-ae96ef1b2c768�٪�ě�@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�GZH�          021_download,6f5c3b20-bd2d-44ce-a862-a6881455cfee�
�
$6f5c3b20-bd2d-44ce-a862-a6881455cfee
���������"�
https://optimizationguide-pa.googleapis.com/downloads?name=1728324084&target=OPTIMIZATION_TARGET_OMNIBOX_ON_DEVICE_TAIL_SUGGEST " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�ֶZ	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 f 5 c 3 b 2 0 - b d 2 d - 4 4 c e - a 8 6 2 - a 6 8 8 1 4 5 5 c f e e   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 6 f 5 c 3 b 2 0 - b d 2 d - 4 4 c e - a 8 6 2 - a 6 8 8 1 4 5 5 c f e e   x�ֶ�Â���������� ݜ泮Y��'���<�/n�D��Y �4������ � � � � ����������������������


     ���> �           021_download,6f5c3b20-bd2d-44ce-a862-a6881455cfee^t����          021_download,163b2ad6-1b2d-49d4-bef2-90238f0d103e�
�
$163b2ad6-1b2d-49d4-bef2-90238f0d103e
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj       r       x �ڌ�������������� �� � � � � � � ����������������������


     X�"b�          &9_163b2ad6-1b2d-49d4-bef2-90238f0d103e�	$163b2ad6-1b2d-49d4-bef2-90238f0d103e��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\163b2ad6-1b2d-49d4-bef2-90238f0d103e8�٪�ě�@ HPϟ�/X ` p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH898f31KCKeOAf50_rlDhetclqdr43JCEmvRCgcN-ESuZ6oOWGpRqZVMfEF32WgQ1Cq8 vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:55 GMT expires:Fri, 30 May 2025 13:09:55 GMT accept-ranges:bytes x-goog-hash:crc32c=Z6DaGA== content-length:4680 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING���,�          021_download,163b2ad6-1b2d-49d4-bef2-90238f0d103e�
�
$163b2ad6-1b2d-49d4-bef2-90238f0d103e
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   5 7 8 8 4 8 . c r d o w n l o a d r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 6 3 b 2 a d 6 - 1 b 2 d - 4 9 d 4 - b e f 2 - 9 0 2 3 8 f 0 d 1 0 3 e   x�$�ڌ�������������� ��0���r�*�:���X�t����V���� �� � � � � ����������������������


     `�R&��          021_download,a34403c9-a55f-4129-a537-ae96ef1b2c76�
�
$a34403c9-a55f-4129-a537-ae96ef1b2c76	
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x �ƍ�������������� �� � � � � � � ����������������������


     ��U-��          &9_a34403c9-a55f-4129-a537-ae96ef1b2c76�	$a34403c9-a55f-4129-a537-ae96ef1b2c76��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\a34403c9-a55f-4129-a537-ae96ef1b2c768�٪�ě�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_EIgD7lm9EzcxkIlot2mRlec0mCyaK_dkjuVkF6bfcrd2Jw8rp580J7jduhlCeUT_P vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:55 GMT expires:Fri, 30 May 2025 13:09:55 GMT accept-ranges:bytes x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONKfV�          021_download,a34403c9-a55f-4129-a537-ae96ef1b2c76�
�
$a34403c9-a55f-4129-a537-ae96ef1b2c76	
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�   �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ U n c o n f i r m e d   2 2 2 0 3 . c r d o w n l o a d   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 3 4 4 0 3 c 9 - a 5 5 f - 4 1 2 9 - a 5 3 7 - a e 9 6 e f 1 b 2 c 7 6   x �ƍ�������������� �� �� � � � � ����������������������


     ��O�9�          021_download,163b2ad6-1b2d-49d4-bef2-90238f0d103e�
�
$163b2ad6-1b2d-49d4-bef2-90238f0d103e
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 6 3 b 2 a d 6 - 1 b 2 d - 4 9 d 4 - b e f 2 - 9 0 2 3 8 f 0 d 1 0 3 e   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 6 3 b 2 a d 6 - 1 b 2 d - 4 9 d 4 - b e f 2 - 9 0 2 3 8 f 0 d 1 0 3 e   x�$�ڌ���������� ��0���r�*�:���X�t����V������ � � � � ����������������������


     ��qq�          &9_163b2ad6-1b2d-49d4-bef2-90238f0d103e�	$163b2ad6-1b2d-49d4-bef2-90238f0d103e��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\163b2ad6-1b2d-49d4-bef2-90238f0d103e8�٪�ě�@���ě�HPϟ�/X�$`���ě�p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH898f31KCKeOAf50_rlDhetclqdr43JCEmvRCgcN-ESuZ6oOWGpRqZVMfEF32WgQ1Cq8 vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:55 GMT expires:Fri, 30 May 2025 13:09:55 GMT accept-ranges:bytes x-goog-hash:crc32c=Z6DaGA== content-length:4680 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�Ɋ�9�          021_download,163b2ad6-1b2d-49d4-bef2-90238f0d103e�
�
$163b2ad6-1b2d-49d4-bef2-90238f0d103e
���������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696267841&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�$Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 6 3 b 2 a d 6 - 1 b 2 d - 4 9 d 4 - b e f 2 - 9 0 2 3 8 f 0 d 1 0 3 e   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 6 3 b 2 a d 6 - 1 b 2 d - 4 9 d 4 - b e f 2 - 9 0 2 3 8 f 0 d 1 0 3 e   x�$�ڌ���������� ��0���r�*�:���X�t����V������ � � � � ����������������������


     n���> �           021_download,163b2ad6-1b2d-49d4-bef2-90238f0d103e��,�I�          021_download,a34403c9-a55f-4129-a537-ae96ef1b2c76�
�
$a34403c9-a55f-4129-a537-ae96ef1b2c76	
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 3 4 4 0 3 c 9 - a 5 5 f - 4 1 2 9 - a 5 3 7 - a e 9 6 e f 1 b 2 c 7 6   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 3 4 4 0 3 c 9 - a 5 5 f - 4 1 2 9 - a 5 3 7 - a e 9 6 e f 1 b 2 c 7 6   x���ƍ���������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     L!xߛ�          &9_a34403c9-a55f-4129-a537-ae96ef1b2c76�	$a34403c9-a55f-4129-a537-ae96ef1b2c76��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2�C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_fightzviral\Default\Download Service\Files\a34403c9-a55f-4129-a537-ae96ef1b2c768�٪�ě�@ҩ��ě�HPϟ�/X��`ҩ��ě�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_EIgD7lm9EzcxkIlot2mRlec0mCyaK_dkjuVkF6bfcrd2Jw8rp580J7jduhlCeUT_P vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Thu, 29 May 2025 13:09:55 GMT expires:Fri, 30 May 2025 13:09:55 GMT accept-ranges:bytes x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION����I�          021_download,a34403c9-a55f-4129-a537-ae96ef1b2c76�
�
$a34403c9-a55f-4129-a537-ae96ef1b2c76	
���������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 3 4 4 0 3 c 9 - a 5 5 f - 4 1 2 9 - a 5 3 7 - a e 9 6 e f 1 b 2 c 7 6   r�0  �   C : \ U s e r s \ A d m i n i s t r a t o r \ P y c h a r m P r o j e c t s \ S o r c e r i o M o d u l e s \ c h r o m e _ p r o f i l e _ f i g h t z v i r a l \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 3 4 4 0 3 c 9 - a 5 5 f - 4 1 2 9 - a 5 3 7 - a e 9 6 e f 1 b 2 c 7 6   x���ƍ���������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     
*�V> �           021_download,a34403c9-a55f-4129-a537-ae96ef1b2c76�1s�F�          	39_config�
��؈��O�ԓ �ۣ�1
����Ą���ԓ �ۣ�1
�����ٝ���ԓ �ۣ�1
�����ؿ���ԓ �ۣ�1
�ހ���`�ԓ �ۣ�1
"�������d�ԓ �ۣ�1(���ʖ����
ۯ��Њ���ԓ �ۣ�1
��՛�����ԓ �ۣ�1
���Åօ�C�ԓ �ۣ�1
�����Ӆ���ԓ �ۣ�1
��������_�ԓ �ۣ�1
�����������I �ۣ�1
�������I �ۣ�1
ʒ���қ�C��I �ۣ�1
���޾���,��I �ۣ�1
���������I �ۣ�1
������t�ԓ �ۣ�1
��������k�ԓ �ۣ�1
գ��������ԓ �ۣ�1
��ר�ٳ���ԓ �ۣ�1
ෛ�������ԓ �ۣ�1
������Ʉ��ԓ �ۣ�1
"��ї�Z�ԓ �ۣ�1(Ȏ�������
#�򖐩�����ԓ �ۣ�1(Ȏ�������
#�ɕԺ����ԓ �ۣ�1(Ȏ�������
!������վN�ԓ �ۣ�1(��������'
"��������ԓ �ۣ�1(��������'
"��ڀ����ԓ �ۣ�1(��������'
!���䍟��B�ԓ �ۣ�1(��������'
"����̂呮�ԓ �ۣ�1(��������'
#������Ơ��ԓ �ۣ�1(��袺ص��
#�풠�����ԓ �ۣ�1(��袺ص��
!�����Ù��ԓ �ۣ�1(�ٴ�ڥ�7
!���������ԓ �ۣ�1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ �ۣ�1(�ٴ�ڥ�7
!������ڷu�ԓ �ۣ�1(�ٴ�ڥ�7��ܣd�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10:c�w �           :c�w �           :c�w �           �;��} �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagement�,� Other (�ۣ�10~t��          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10[�VL �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10��c�� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10�鏓` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10�����          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10��6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10)�6a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�10���EW�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10!�1� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10p[d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10���� �           ���� �           ���� �           ���� �           �ξ�� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�10�-Y���          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10��do �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10Bp�I� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10R
�` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10~�*���          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10p�Y66�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10��X�a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�10���W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10�m��� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10�&��d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10�q�� �           �q�� �           �q�� �           �q�� �           �=�&� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�10��ސ�          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10���� �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10x@�� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10�fj�` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10*I;���          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10�Pug6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10i�^a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�10���+W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10G�b{� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10���d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10@�+� �           �W�5: �          #38_h       k�l��,�   H�1   H�1
 ��i� �          20_1_6�s~	 2
510 �]�/ �           �]�/ �           �]�/ �           ��CW� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�10c	����          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10��Q �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10��� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10э` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10T�ږ��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10H���6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10�/�sa�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�10$TU=W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10����� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10�0}d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10i�2� �           i�2� �           i�2� �           i�2� �           ��� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�10B��א�          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�106�� �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10��&
� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10y�&E` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10z4v��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10R;�6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10A�O�a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�106��W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10'��� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10�e��d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10⹊� �           ⹊� �           ⹊� �           ⹊� �           ⹊� �           ⹊� �           ⹊� �           ⹊� �           ⹊� �           hܨq� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�10�����          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10��$� �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10;���� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10VQ�` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10	X}��          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10»I6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10���:a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�10�H�W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10f�F�� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10k����          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_�/�-� urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10�BM �           �BM �           �BM �           �BM �           �=Z� �          
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ۣ�107L���          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    ����ě�$


   ?ShoppingUserOther  (�ۣ�10��� �          
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ۣ�10���� �          
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ۣ�10�/�"` �          
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ۣ�10o�x���          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ۣ�10[E��6�          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ۣ�10A��-a�          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ۣ�104���W�          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ۣ�10�^� �          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ۣ�10=�d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�ۣ�10��� �           